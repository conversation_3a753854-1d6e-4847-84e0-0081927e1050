# Azure Database settings for Pri Fashion
# Replace these values with your actual Azure Database details

# Database mode
DATABASE_MODE=azure

# Azure Database for MySQL settings
DATABASE_NAME=prifashion
DATABASE_USER=prifashionadmin
DATABASE_PASSWORD=your_azure_password_here
DATABASE_HOST=prifashion-db-server.mysql.database.azure.com
DATABASE_PORT=3306

# SSL Configuration for Azure
DATABASE_SSL_MODE=REQUIRED

# Python path
PYTHON_PATH=

# Media settings
MEDIA_ROOT=media

# Debug settings (set to False for production)
DEBUG=False

# Secret key (generate a new one for production)
SECRET_KEY=django-insecure-ge9)a+1@8zel*ba@&m$4ltn-2nhoc0$)y1%#t15@4cn7o38052

# Azure specific settings
AZURE_STORAGE_ACCOUNT_NAME=
AZURE_STORAGE_ACCOUNT_KEY=
AZURE_STORAGE_CONTAINER_NAME=media
