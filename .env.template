# Database settings - Local MySQL (for development)
DATABASE_NAME=prifashion
DATABASE_USER=root
DATABASE_PASSWORD=boossa12
DATABASE_HOST=localhost
DATABASE_PORT=3306

# Azure Database settings - Cloud MySQL (for production)
AZURE_DATABASE_NAME=prifashion
AZURE_DATABASE_USER=prifashionadmin
AZURE_DATABASE_PASSWORD=your_azure_password_here
AZURE_DATABASE_HOST=prifashion-db-server.mysql.database.azure.com
AZURE_DATABASE_PORT=3306

# Database mode: 'local' or 'azure'
DATABASE_MODE=local

# Python path
PYTHON_PATH=

# Media settings
MEDIA_ROOT=media

# Debug settings
DEBUG=True

# Secret key (generate a new one for production)
SECRET_KEY=django-insecure-ge9)a+1@8zel*ba@&m$4ltn-2nhoc0$)y1%#t15@4cn7o38052
