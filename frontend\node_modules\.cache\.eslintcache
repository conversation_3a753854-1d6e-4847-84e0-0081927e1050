[{"C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\index.js": "1", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\reportWebVitals.js": "2", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\Signup.js": "3", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\Login.js": "4", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OwnerDashboard.js": "5", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\InventoryDashboard.js": "6", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OrdersDashboard.js": "7", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesDashboard.js": "8", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddSupplier.js": "9", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddFabric.js": "10", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\EditFabric.js": "11", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewFabrics.js": "12", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewFabricVariants.js": "13", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewCutting.js": "14", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddCutting.js": "15", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddDailySewingRecord.js": "16", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewDailySewingHistory.js": "17", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewProductList.js": "18", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ApproveFinishedProduct.js": "19", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewApproveProduct.js": "20", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddPackingSession.js": "21", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\PackingReportChart.js": "22", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\CreateOrder.js": "23", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddShop.js": "24", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OrderListPage.js.js": "25", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\RoleBasedNavBar.js": "26", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\auth.js": "27", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\OwnerNavBar.js": "28", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\SalesTeamNavBar.js": "29", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\OrderCoordinatorNavBar.js": "30", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\App.js": "31", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewPackingSessions.js": "32", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewSuppliers.js": "33", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\DashboardCard.js": "34", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OwnerOrdersPage.js": "35", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\PaymentModal.js": "36", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\DeliveryModal.js": "37", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\InvoicePreviewModal.js": "38", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesProductView.js": "39", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesProductImageViewer.js": "40", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\firebase\\imageUpload.js": "41", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\firebase\\config.js": "42", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\imageUpload.js": "43", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesTeamOrdersPage.js": "44", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ShopAnalysisDashboard.js": "45", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\ShopDistrictAnalysis.js": "46", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewPackingInventory.js": "47", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\api.js": "48", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OrderAnalysisPage.js": "49", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\axiosConfig.js": "50", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\FabricInventoryDetail.js": "51", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\CuttingRecordDetail.js": "52", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\ProductAnalysis.js": "53", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\SingleProductAnalysis.js": "54", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SellProductPage.js": "55", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewPackingInventorySales.js": "56", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\EditCutting.js": "57", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\RevertOrderModal.js": "58", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\ProtectedRoute.js": "59", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\InventoryManagerNavBar.js": "60", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewShops.js": "61", "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesReport.js": "62"}, {"size": 685, "mtime": 1746730728945, "results": "63", "hashOfConfig": "64"}, {"size": 362, "mtime": 1741448182472, "results": "65", "hashOfConfig": "64"}, {"size": 26301, "mtime": 1747563605197, "results": "66", "hashOfConfig": "64"}, {"size": 14310, "mtime": 1751256337391, "results": "67", "hashOfConfig": "64"}, {"size": 79153, "mtime": 1748118376561, "results": "68", "hashOfConfig": "64"}, {"size": 42225, "mtime": 1747825393102, "results": "69", "hashOfConfig": "64"}, {"size": 14637, "mtime": 1747666699582, "results": "70", "hashOfConfig": "64"}, {"size": 6098, "mtime": 1746991001290, "results": "71", "hashOfConfig": "64"}, {"size": 13849, "mtime": 1746431589623, "results": "72", "hashOfConfig": "64"}, {"size": 15017, "mtime": 1747662210809, "results": "73", "hashOfConfig": "64"}, {"size": 20894, "mtime": 1747469446553, "results": "74", "hashOfConfig": "64"}, {"size": 29956, "mtime": 1748411988604, "results": "75", "hashOfConfig": "64"}, {"size": 44234, "mtime": 1747575219913, "results": "76", "hashOfConfig": "64"}, {"size": 33419, "mtime": 1751441819466, "results": "77", "hashOfConfig": "64"}, {"size": 32138, "mtime": 1751444413117, "results": "78", "hashOfConfig": "64"}, {"size": 33108, "mtime": 1748118376561, "results": "79", "hashOfConfig": "64"}, {"size": 12807, "mtime": 1747635375704, "results": "80", "hashOfConfig": "64"}, {"size": 33531, "mtime": 1748411988604, "results": "81", "hashOfConfig": "64"}, {"size": 56491, "mtime": 1748118376561, "results": "82", "hashOfConfig": "64"}, {"size": 99330, "mtime": 1747638263490, "results": "83", "hashOfConfig": "64"}, {"size": 14435, "mtime": 1746792888852, "results": "84", "hashOfConfig": "64"}, {"size": 2458, "mtime": 1746527664333, "results": "85", "hashOfConfig": "64"}, {"size": 50544, "mtime": 1748411988604, "results": "86", "hashOfConfig": "64"}, {"size": 24066, "mtime": 1746538850551, "results": "87", "hashOfConfig": "64"}, {"size": 17459, "mtime": 1746720981418, "results": "88", "hashOfConfig": "64"}, {"size": 1786, "mtime": 1746420202184, "results": "89", "hashOfConfig": "64"}, {"size": 2550, "mtime": 1746730787992, "results": "90", "hashOfConfig": "64"}, {"size": 7986, "mtime": 1747662603286, "results": "91", "hashOfConfig": "64"}, {"size": 7639, "mtime": 1747666699579, "results": "92", "hashOfConfig": "64"}, {"size": 11014, "mtime": 1747662603336, "results": "93", "hashOfConfig": "64"}, {"size": 10319, "mtime": 1747666699576, "results": "94", "hashOfConfig": "64"}, {"size": 8358, "mtime": 1746603854720, "results": "95", "hashOfConfig": "64"}, {"size": 13530, "mtime": 1747467936684, "results": "96", "hashOfConfig": "64"}, {"size": 1560, "mtime": 1746432486037, "results": "97", "hashOfConfig": "64"}, {"size": 53281, "mtime": 1747585197033, "results": "98", "hashOfConfig": "64"}, {"size": 11059, "mtime": 1746529408005, "results": "99", "hashOfConfig": "64"}, {"size": 5593, "mtime": 1746471719124, "results": "100", "hashOfConfig": "64"}, {"size": 9995, "mtime": 1747666699579, "results": "101", "hashOfConfig": "64"}, {"size": 25701, "mtime": 1746991938861, "results": "102", "hashOfConfig": "64"}, {"size": 14826, "mtime": 1747294713548, "results": "103", "hashOfConfig": "64"}, {"size": 3133, "mtime": 1746518864186, "results": "104", "hashOfConfig": "105"}, {"size": 1301, "mtime": 1746519201174, "results": "106", "hashOfConfig": "105"}, {"size": 2867, "mtime": 1746521061546, "results": "107", "hashOfConfig": "105"}, {"size": 23488, "mtime": 1746791397626, "results": "108", "hashOfConfig": "64"}, {"size": 5623, "mtime": 1746538824306, "results": "109", "hashOfConfig": "64"}, {"size": 6130, "mtime": 1746538901554, "results": "110", "hashOfConfig": "64"}, {"size": 48259, "mtime": 1747633019998, "results": "111", "hashOfConfig": "64"}, {"size": 2374, "mtime": 1748411988604, "results": "112", "hashOfConfig": "64"}, {"size": 36946, "mtime": 1746791096234, "results": "113", "hashOfConfig": "64"}, {"size": 1578, "mtime": 1747821271531, "results": "114", "hashOfConfig": "64"}, {"size": 11121, "mtime": 1746888196660, "results": "115", "hashOfConfig": "64"}, {"size": 23474, "mtime": 1751441892097, "results": "116", "hashOfConfig": "64"}, {"size": 11708, "mtime": 1747036435131, "results": "117", "hashOfConfig": "105"}, {"size": 10269, "mtime": 1747144038017, "results": "118", "hashOfConfig": "105"}, {"size": 28364, "mtime": 1747284313303, "results": "119", "hashOfConfig": "64"}, {"size": 111, "mtime": 1747666699584, "results": "120", "hashOfConfig": "64"}, {"size": 29447, "mtime": 1747488585963, "results": "121", "hashOfConfig": "105"}, {"size": 6380, "mtime": 1747492770045, "results": "122", "hashOfConfig": "64"}, {"size": 1883, "mtime": 1747494539190, "results": "123", "hashOfConfig": "64"}, {"size": 8076, "mtime": 1747666699578, "results": "124", "hashOfConfig": "64"}, {"size": 8673, "mtime": 1748411988604, "results": "125", "hashOfConfig": "64"}, {"size": 26457, "mtime": 1748118376561, "results": "126", "hashOfConfig": "64"}, {"filePath": "127", "messages": "128", "suppressedMessages": "129", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "bihdxj", {"filePath": "130", "messages": "131", "suppressedMessages": "132", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "133", "messages": "134", "suppressedMessages": "135", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "136", "messages": "137", "suppressedMessages": "138", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "139", "messages": "140", "suppressedMessages": "141", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "142", "messages": "143", "suppressedMessages": "144", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "145", "messages": "146", "suppressedMessages": "147", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "148", "messages": "149", "suppressedMessages": "150", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "151", "messages": "152", "suppressedMessages": "153", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "154", "messages": "155", "suppressedMessages": "156", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "157", "messages": "158", "suppressedMessages": "159", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "160", "messages": "161", "suppressedMessages": "162", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "163", "messages": "164", "suppressedMessages": "165", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "166", "messages": "167", "suppressedMessages": "168", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "169", "messages": "170", "suppressedMessages": "171", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "172", "messages": "173", "suppressedMessages": "174", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "175", "messages": "176", "suppressedMessages": "177", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "178", "messages": "179", "suppressedMessages": "180", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "181", "messages": "182", "suppressedMessages": "183", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "184", "messages": "185", "suppressedMessages": "186", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "187", "messages": "188", "suppressedMessages": "189", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "190", "messages": "191", "suppressedMessages": "192", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "193", "messages": "194", "suppressedMessages": "195", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "196", "messages": "197", "suppressedMessages": "198", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "199", "messages": "200", "suppressedMessages": "201", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "202", "messages": "203", "suppressedMessages": "204", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "205", "messages": "206", "suppressedMessages": "207", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "208", "messages": "209", "suppressedMessages": "210", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "211", "messages": "212", "suppressedMessages": "213", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "214", "messages": "215", "suppressedMessages": "216", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "217", "messages": "218", "suppressedMessages": "219", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "220", "messages": "221", "suppressedMessages": "222", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "223", "messages": "224", "suppressedMessages": "225", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "226", "messages": "227", "suppressedMessages": "228", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "229", "messages": "230", "suppressedMessages": "231", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "232", "messages": "233", "suppressedMessages": "234", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "235", "messages": "236", "suppressedMessages": "237", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "238", "messages": "239", "suppressedMessages": "240", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "241", "messages": "242", "suppressedMessages": "243", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "244", "messages": "245", "suppressedMessages": "246", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "247", "messages": "248", "suppressedMessages": "249", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, "frlojg", {"filePath": "250", "messages": "251", "suppressedMessages": "252", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "253", "messages": "254", "suppressedMessages": "255", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "256", "messages": "257", "suppressedMessages": "258", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "259", "messages": "260", "suppressedMessages": "261", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "262", "messages": "263", "suppressedMessages": "264", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "265", "messages": "266", "suppressedMessages": "267", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "268", "messages": "269", "suppressedMessages": "270", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "271", "messages": "272", "suppressedMessages": "273", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "274", "messages": "275", "suppressedMessages": "276", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "277", "messages": "278", "suppressedMessages": "279", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "280", "messages": "281", "suppressedMessages": "282", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "283", "messages": "284", "suppressedMessages": "285", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "286", "messages": "287", "suppressedMessages": "288", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "289", "messages": "290", "suppressedMessages": "291", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 6, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "292", "messages": "293", "suppressedMessages": "294", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "295", "messages": "296", "suppressedMessages": "297", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "298", "messages": "299", "suppressedMessages": "300", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "301", "messages": "302", "suppressedMessages": "303", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "304", "messages": "305", "suppressedMessages": "306", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "307", "messages": "308", "suppressedMessages": "309", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "310", "messages": "311", "suppressedMessages": "312", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\index.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\reportWebVitals.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\Signup.js", ["313", "314", "315"], ["316"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\Login.js", ["317"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OwnerDashboard.js", [], ["318"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\InventoryDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OrdersDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesDashboard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddSupplier.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddFabric.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\EditFabric.js", ["319", "320"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewFabrics.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewFabricVariants.js", ["321", "322", "323", "324"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewCutting.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddCutting.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddDailySewingRecord.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewDailySewingHistory.js", ["325"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewProductList.js", ["326"], ["327"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ApproveFinishedProduct.js", ["328", "329", "330", "331", "332"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewApproveProduct.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddPackingSession.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\PackingReportChart.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\CreateOrder.js", ["333", "334", "335"], ["336"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\AddShop.js", ["337"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OrderListPage.js.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\RoleBasedNavBar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\auth.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\OwnerNavBar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\SalesTeamNavBar.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\OrderCoordinatorNavBar.js", ["338"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\App.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewPackingSessions.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewSuppliers.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\DashboardCard.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OwnerOrdersPage.js", [], ["339"], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\PaymentModal.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\DeliveryModal.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\InvoicePreviewModal.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesProductView.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesProductImageViewer.js", ["340"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\firebase\\imageUpload.js", ["341", "342"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\firebase\\config.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\imageUpload.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesTeamOrdersPage.js", ["343", "344"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ShopAnalysisDashboard.js", ["345"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\ShopDistrictAnalysis.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewPackingInventory.js", ["346", "347", "348"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\api.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\OrderAnalysisPage.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\utils\\axiosConfig.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\FabricInventoryDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\CuttingRecordDetail.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\ProductAnalysis.js", ["349", "350"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\SingleProductAnalysis.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SellProductPage.js", ["351", "352", "353", "354", "355", "356"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewPackingInventorySales.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\EditCutting.js", ["357", "358", "359", "360", "361", "362", "363"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\RevertOrderModal.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\ProtectedRoute.js", [], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\components\\InventoryManagerNavBar.js", ["364"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\ViewShops.js", ["365", "366", "367"], [], "C:\\Users\\<USER>\\OneDrive\\Desktop\\System Development Project\\pri new\\frontend\\src\\pages\\SalesReport.js", [], [], {"ruleId": "368", "severity": 1, "message": "369", "line": 27, "column": 20, "nodeType": "370", "messageId": "371", "endLine": 27, "endColumn": 31}, {"ruleId": "368", "severity": 1, "message": "372", "line": 36, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 36, "endColumn": 27}, {"ruleId": "368", "severity": 1, "message": "373", "line": 36, "column": 29, "nodeType": "370", "messageId": "371", "endLine": 36, "endColumn": 49}, {"ruleId": "374", "severity": 1, "message": "375", "line": 101, "column": 6, "nodeType": "376", "endLine": 101, "endColumn": 16, "suggestions": "377", "suppressions": "378"}, {"ruleId": "368", "severity": 1, "message": "379", "line": 11, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 11, "endColumn": 10}, {"ruleId": "374", "severity": 1, "message": "380", "line": 295, "column": 6, "nodeType": "376", "endLine": 295, "endColumn": 8, "suggestions": "381", "suppressions": "382"}, {"ruleId": "368", "severity": 1, "message": "383", "line": 9, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 9, "endColumn": 21}, {"ruleId": "368", "severity": 1, "message": "384", "line": 42, "column": 30, "nodeType": "370", "messageId": "371", "endLine": 42, "endColumn": 51}, {"ruleId": "368", "severity": 1, "message": "383", "line": 5, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 5, "endColumn": 21}, {"ruleId": "368", "severity": 1, "message": "385", "line": 9, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 9, "endColumn": 7}, {"ruleId": "368", "severity": 1, "message": "386", "line": 14, "column": 41, "nodeType": "370", "messageId": "371", "endLine": 14, "endColumn": 47}, {"ruleId": "368", "severity": 1, "message": "384", "line": 28, "column": 30, "nodeType": "370", "messageId": "371", "endLine": 28, "endColumn": 51}, {"ruleId": "368", "severity": 1, "message": "387", "line": 19, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 19, "endColumn": 20}, {"ruleId": "368", "severity": 1, "message": "388", "line": 5, "column": 23, "nodeType": "370", "messageId": "371", "endLine": 5, "endColumn": 30}, {"ruleId": "374", "severity": 1, "message": "389", "line": 177, "column": 6, "nodeType": "376", "endLine": 177, "endColumn": 8, "suggestions": "390", "suppressions": "391"}, {"ruleId": "368", "severity": 1, "message": "392", "line": 8, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 8, "endColumn": 15}, {"ruleId": "368", "severity": 1, "message": "393", "line": 35, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 35, "endColumn": 20}, {"ruleId": "368", "severity": 1, "message": "394", "line": 35, "column": 22, "nodeType": "370", "messageId": "371", "endLine": 35, "endColumn": 35}, {"ruleId": "368", "severity": 1, "message": "395", "line": 216, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 216, "endColumn": 25}, {"ruleId": "368", "severity": 1, "message": "396", "line": 255, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 255, "endColumn": 23}, {"ruleId": "368", "severity": 1, "message": "397", "line": 76, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 76, "endColumn": 21}, {"ruleId": "368", "severity": 1, "message": "398", "line": 76, "column": 23, "nodeType": "370", "messageId": "371", "endLine": 76, "endColumn": 37}, {"ruleId": "368", "severity": 1, "message": "399", "line": 672, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 672, "endColumn": 20}, {"ruleId": "374", "severity": 1, "message": "400", "line": 230, "column": 6, "nodeType": "376", "endLine": 230, "endColumn": 8, "suggestions": "401", "suppressions": "402"}, {"ruleId": "368", "severity": 1, "message": "403", "line": 77, "column": 12, "nodeType": "370", "messageId": "371", "endLine": 77, "endColumn": 25}, {"ruleId": "368", "severity": 1, "message": "404", "line": 5, "column": 37, "nodeType": "370", "messageId": "371", "endLine": 5, "endColumn": 48}, {"ruleId": "368", "severity": 1, "message": "405", "line": 230, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 230, "endColumn": 28, "suppressions": "406"}, {"ruleId": "368", "severity": 1, "message": "407", "line": 4, "column": 59, "nodeType": "370", "messageId": "371", "endLine": 4, "endColumn": 66}, {"ruleId": "408", "severity": 1, "message": "409", "line": 71, "column": 9, "nodeType": "410", "messageId": "411", "endLine": 79, "endColumn": 10}, {"ruleId": "408", "severity": 1, "message": "409", "line": 83, "column": 26, "nodeType": "410", "messageId": "411", "endLine": 85, "endColumn": 8}, {"ruleId": "368", "severity": 1, "message": "412", "line": 2, "column": 63, "nodeType": "370", "messageId": "371", "endLine": 2, "endColumn": 76}, {"ruleId": "368", "severity": 1, "message": "413", "line": 2, "column": 78, "nodeType": "370", "messageId": "371", "endLine": 2, "endColumn": 88}, {"ruleId": "368", "severity": 1, "message": "414", "line": 3, "column": 19, "nodeType": "370", "messageId": "371", "endLine": 3, "endColumn": 29}, {"ruleId": "368", "severity": 1, "message": "415", "line": 31, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 31, "endColumn": 9}, {"ruleId": "368", "severity": 1, "message": "416", "line": 33, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 33, "endColumn": 12}, {"ruleId": "368", "severity": 1, "message": "417", "line": 39, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 39, "endColumn": 12}, {"ruleId": "368", "severity": 1, "message": "418", "line": 11, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 11, "endColumn": 21}, {"ruleId": "368", "severity": 1, "message": "419", "line": 20, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 20, "endColumn": 23}, {"ruleId": "368", "severity": 1, "message": "420", "line": 3, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 3, "endColumn": 17}, {"ruleId": "368", "severity": 1, "message": "379", "line": 3, "column": 19, "nodeType": "370", "messageId": "371", "endLine": 3, "endColumn": 26}, {"ruleId": "368", "severity": 1, "message": "421", "line": 3, "column": 28, "nodeType": "370", "messageId": "371", "endLine": 3, "endColumn": 42}, {"ruleId": "368", "severity": 1, "message": "422", "line": 10, "column": 25, "nodeType": "370", "messageId": "371", "endLine": 10, "endColumn": 41}, {"ruleId": "374", "severity": 1, "message": "423", "line": 71, "column": 6, "nodeType": "376", "endLine": 71, "endColumn": 28, "suggestions": "424"}, {"ruleId": "368", "severity": 1, "message": "425", "line": 111, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 111, "endColumn": 30}, {"ruleId": "368", "severity": 1, "message": "426", "line": 4, "column": 8, "nodeType": "370", "messageId": "371", "endLine": 4, "endColumn": 14}, {"ruleId": "368", "severity": 1, "message": "427", "line": 6, "column": 74, "nodeType": "370", "messageId": "371", "endLine": 6, "endColumn": 79}, {"ruleId": "368", "severity": 1, "message": "428", "line": 43, "column": 10, "nodeType": "370", "messageId": "371", "endLine": 43, "endColumn": 24}, {"ruleId": "429", "severity": 1, "message": "430", "line": 168, "column": 56, "nodeType": "431", "messageId": "432", "endLine": 168, "endColumn": 58}, {"ruleId": "374", "severity": 1, "message": "433", "line": 222, "column": 6, "nodeType": "376", "endLine": 222, "endColumn": 50, "suggestions": "434"}, {"ruleId": "368", "severity": 1, "message": "435", "line": 340, "column": 13, "nodeType": "370", "messageId": "371", "endLine": 340, "endColumn": 21}, {"ruleId": "368", "severity": 1, "message": "436", "line": 364, "column": 9, "nodeType": "370", "messageId": "371", "endLine": 364, "endColumn": 21}, {"ruleId": "368", "severity": 1, "message": "437", "line": 10, "column": 3, "nodeType": "370", "messageId": "371", "endLine": 10, "endColumn": 13}, {"ruleId": "368", "severity": 1, "message": "438", "line": 3, "column": 21, "nodeType": "370", "messageId": "371", "endLine": 3, "endColumn": 24}, {"ruleId": "368", "severity": 1, "message": "439", "line": 3, "column": 26, "nodeType": "370", "messageId": "371", "endLine": 3, "endColumn": 29}, {"ruleId": "368", "severity": 1, "message": "386", "line": 4, "column": 62, "nodeType": "370", "messageId": "371", "endLine": 4, "endColumn": 68}, "no-unused-vars", "'setUserRole' is assigned a value but never used.", "Identifier", "unusedVar", "'editPasswordError' is assigned a value but never used.", "'setEditPasswordError' is assigned a value but never used.", "react-hooks/exhaustive-deps", "React Hook useEffect has a missing dependency: 'fetchUsers'. Either include it or remove the dependency array.", "ArrayExpression", ["440"], ["441"], "'FaBoxes' is defined but never used.", "React Hook useEffect has a missing dependency: 'timeFrame'. Either include it or remove the dependency array.", ["442"], ["443"], "'getUserRole' is defined but never used.", "'setIsInventoryManager' is assigned a value but never used.", "'Form' is defined but never used.", "'FaEdit' is defined but never used.", "'totalItems' is assigned a value but never used.", "'hasRole' is defined but never used.", "React Hook useEffect has a missing dependency: 'fetchProducts'. Either include it or remove the dependency array.", ["444"], ["445"], "'FaArrowRight' is defined but never used.", "'isDragging' is assigned a value but never used.", "'setIsDragging' is assigned a value but never used.", "'triggerFileInput' is assigned a value but never used.", "'setActiveImage' is assigned a value but never used.", "'isInfoModal' is assigned a value but never used.", "'setIsInfoModal' is assigned a value but never used.", "'getShopName' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'fetchData'. Either include it or remove the dependency array.", ["446"], ["447"], "'locationError' is assigned a value but never used.", "'FaChartLine' is defined but never used.", "'handleMarkDelivered' is assigned a value but never used.", ["448"], "'FaTimes' is defined but never used.", "no-loop-func", "Function declared in a loop contains unsafe references to variable(s) 'completedFiles'.", "ArrowFunctionExpression", "unsafeRefs", "'FaFileInvoice' is defined but never used.", "'FaDownload' is defined but never used.", "'FaChartPie' is defined but never used.", "'BsShop' is defined but never used.", "'BsGraphUp' is defined but never used.", "'FaHistory' is defined but never used.", "'productData' is assigned a value but never used.", "'formatCurrency' is assigned a value but never used.", "'FaStore' is defined but never used.", "'FaShoppingCart' is defined but never used.", "'setIsSidebarOpen' is assigned a value but never used.", "React Hook useEffect has a missing dependency: 'addProductToSelection'. Either include it or remove the dependency array.", ["449"], "'updateProductQuantity' is assigned a value but never used.", "'Select' is defined but never used.", "'Modal' is defined but never used.", "'originalRecord' is assigned a value but never used.", "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "React Hook useEffect has a missing dependency: 'detailErrors'. Either include it or remove the dependency array.", ["450"], "'response' is assigned a value but never used.", "'ColourOption' is assigned a value but never used.", "'FaBuilding' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", {"desc": "451", "fix": "452"}, {"kind": "453", "justification": "454"}, {"desc": "455", "fix": "456"}, {"kind": "453", "justification": "454"}, {"desc": "457", "fix": "458"}, {"kind": "453", "justification": "454"}, {"desc": "459", "fix": "460"}, {"kind": "453", "justification": "454"}, {"kind": "453", "justification": "454"}, {"desc": "461", "fix": "462"}, {"desc": "463", "fix": "464"}, "Update the dependencies array to be: [fetchUsers, userRole]", {"range": "465", "text": "466"}, "directive", "", "Update the dependencies array to be: [timeFrame]", {"range": "467", "text": "468"}, "Update the dependencies array to be: [fetchProducts]", {"range": "469", "text": "470"}, "Update the dependencies array to be: [fetchData]", {"range": "471", "text": "472"}, "Update the dependencies array to be: [addProductToSelection, preSelectedProductId]", {"range": "473", "text": "474"}, "Update the dependencies array to be: [fabricVariants, details, originalYardUsage, detailErrors]", {"range": "475", "text": "476"}, [3791, 3801], "[fetchUsers, userRole]", [11044, 11046], "[timeFrame]", [5738, 5740], "[fetchProducts]", [7406, 7408], "[fetchData]", [2888, 2910], "[addProductToSelection, preSelectedProductId]", [8895, 8939], "[fabricVariants, details, originalYardUsage, detailErrors]"]