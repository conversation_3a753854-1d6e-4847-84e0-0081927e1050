# Network MySQL Database Configuration
# Use this when connecting to MySQL on another computer in your network

# Database mode
DATABASE_MODE=network

# Network MySQL settings (update with actual IP address)
DATABASE_NAME=prifashion
DATABASE_USER=prifashion_user
DATABASE_PASSWORD=prifashion123
DATABASE_HOST=*************
DATABASE_PORT=3306

# Python path
PYTHON_PATH=

# Media settings
MEDIA_ROOT=media

# Debug settings
DEBUG=True

# Secret key
SECRET_KEY=django-insecure-ge9)a+1@8zel*ba@&m$4ltn-2nhoc0$)y1%#t15@4cn7o38052

# Instructions:
# 1. Replace DATABASE_HOST with the actual IP address of the computer running MySQL
# 2. Make sure the MySQL computer is running setup_network_mysql.bat first
# 3. Copy this file to .env to use network database
# 4. Both computers must be on the same network
